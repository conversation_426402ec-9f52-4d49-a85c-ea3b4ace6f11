<template>
  <Drawer
    :open="show"
    width="900px"
    :closable="false"
    placement="right"
    @afterOpenChange="v => !v && modalDestroy()"
    @close="() => modalConfirm({})"
  >
    <Form :model="formState" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
      <div>
        <BorderBox subLabel="实例名称、IP地址、端口号为实例注册时指定" label="基本配置">
          <FormItem label="Workspace">
            <Input v-model:value="formState.workspace" disabled />
          </FormItem>
          <FormItem label="IP地址">
            <Input v-model:value="formState.ip" disabled />
          </FormItem>
          <FormItem label="端口号">
            <Input v-model:value="formState.port" disabled />
          </FormItem>
          <FormItem label="备注名称">
            <Input v-model:value="formState.name" />
          </FormItem>
        </BorderBox>
        <BorderBox subLabel="由实例在注册时指定实例检查类型" label="匹配配置">
          <FormItem label="实例类型">
            <Input v-model:value="formState.instanceType" disabled />
          </FormItem>
        </BorderBox>
      </div>
    </Form>
    <template #title>
      <div class="flex items-center justify-between">
        <div>配置实例</div>
        <Icon :icon="LeftCloseIcon" class="cursor-pointer" @click="close" />
        <Icon :icon="RightCloseIcon" class="cursor-pointer" @click="close" />
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end gap-10px">
        <Button type="primary" @click="save">
          保存
        </Button>
        <Button @click="close">
          取消
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { Button, Drawer, Form, FormItem, Input } from 'ant-design-vue';
import LeftCloseIcon from '@iconify-icons/line-md/arrow-close-left';
import RightCloseIcon from '@iconify-icons/line-md/arrow-close-right';
import BorderBox from '../../../components/BorderBox.vue';
import { reactive, watch } from 'vue';
import type { ExampleConfigItem } from '../../../api';
import { instanceOptions } from './type.data';

const props = defineProps<ModalBaseProps<{ updatedItem?: undefined }> & {
  item?: ExampleConfigItem;
  sentReq: (name: string) => Promise<undefined>;
}>();

const formState = reactive({
  workspace: '',
  ip: '',
  port: '',
  name: '',
  instanceType: '',
});

watch(() => props.item, (val) => {
  formState.name = val?.name || '';
  formState.workspace = val?.workspace || '';
  const url = new URL(val?.ipAddress || '');
  formState.ip = url.hostname || '';
  formState.port = url.port || '';
  formState.instanceType = instanceOptions.find((i) => i.value === val?.instanceType)?.label || '';
}, {
  immediate: true,
});

function close() {
  return props.modalConfirm({});
}
async function save() {
  const updatedItem = await props.sentReq?.(formState.name);
  return props.modalConfirm({ updatedItem });
}
</script>
