<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>审批</span>
        </span>
      </div>
    </template>
    <div class="flex flex-col gap-10px p-20px">
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            审批人：
          </span>
        </Col>
        <Col :span="16">
          <span>
            {{ checkQaList?.map(item => item.nickName).join('、') }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">本地cl：</span>
        </Col>
        <Col :span="16">
          <span>
            {{ shelveCL }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提交人：
          </span>
        </Col>
        <Col :span="16">
          <span>
            {{ submitter?.nickName }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提审时间：
          </span>
        </Col>
        <Col :span="16">
          <span>
            {{ checkTime }}
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提交描述：
          </span>
        </Col>
        <Col :span="16">
          <div>
            {{ description }}
          </div>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            单号：
          </span>
        </Col>
        <Col :span="16">
          <a :href="workItemURL" target="_blank">
            {{ workItemTitle }}
          </a>
        </Col>
      </Row>
    </div>
    <template #footer>
      <div v-if="canApply" class="mt flex justify-center">
        <Button type="primary" @click="handleConfirm(1)">
          通过
        </Button>
        <Button type="primary" danger class="ml-2" @click="handleConfirm(2)">
          拒绝
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Col, Modal, Row } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { Submitter } from '../../../src/api';

const props = defineProps< ModalBaseProps<{ updatedItem?: null }> & {
  checkQaList?: Submitter[];
  shelveCL?: number;
  submitter?: Submitter;
  checkTime?: string;
  description?: string;
  workItemURL?: string;
  workItemTitle?: string;
  canApply?: boolean;
  sentReq?: (formValue: number) => Promise<undefined>;
}>();

async function handleConfirm(approve: number) {
  const updatedItem = await props.sentReq?.(approve);
  return props.modalConfirm({ updatedItem });
}
</script>
