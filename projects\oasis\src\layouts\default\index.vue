<template>
  <Layout :class="prefixCls">
    <AppDarkModeToggle tooltipPlacement="left" />
    <Layout :class="layoutClass">
      <ForgeonSideBar
        v-if="getShowSidebar"
        fixed
        :defaultCollapse="isMenuCollapsed"
        :path="currentPath"
        :modules="authedModules"
        :userInfo="userStore.getUserInfo"
        :token="userStore.getToken"
        :theme="getDarkMode"
        :displayModules="ForgeonMenuItems"
        :onPathChange="onPathChange"
        :onOutsidePathChange="onOutPathChange"
        :onSwitchRole="switchRole"
        :onLogout="userStore.confirmLoginOut"
        :onLogin="onLogin"
        :onToggleDarkMode="handleToggleClick"
        :onCollapseClick="handleCollapseClick"
      />
      <Layout :class="`${prefixCls}-main`">
        <slot>
          <ForgeonHeader
            v-if="!withoutHeader"
            :onHandleMenuExpand="onHandleExpand"
            :showUnfoldIcon="getShowMenuUnfoldBtn"
            :title="currentTitle"
          >
            <template #actions>
              <AppProjectSelect
                v-if="hasToken && getShowProjectSelect"
                :class="`${prefixCls}-project-select`"
                :theme="getDarkMode"
                :showAll="projectShowAllOption"
              />
              <AppHelpDoc v-if="getShowDoc" />
            </template>
          </ForgeonHeader>
          <LayoutContent />
        </slot>
      </Layout>
    </Layout>
  </Layout>
</template>

<script lang="ts" setup>
import { ForgeonTheme } from '@hg-tech/forgeon-style';
import type { RoleListItem } from '/@/api/page/model/systemModel';
import type { RemoveEventFn } from '/@/hooks/event/useEventListener';
import { findItemInMenuByPath, ForgeonHeader, ForgeonSideBar, ModulesMenuConfig, PlatformEnterPoint, useForgeOnSider } from '@hg-tech/oasis-common';
import { computed, onBeforeMount, onMounted, onUnmounted, ref, unref, watch } from 'vue';
import { Layout, message } from 'ant-design-vue';
import LayoutContent from './content/index.vue';
import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
import { type RouteLocationRaw, useRoute, useRouter } from 'vue-router';
import { setUserAuthority } from '/@/api/sys/user';
import { useTrack } from '/@/hooks/system/useTrack';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { useEventListener } from '/@/hooks/event/useEventListener';
import { clearAllFetchPoolLRU, findInTrees } from '@hg-tech/utils';
import { Persistent } from '/@/utils/cache/persistent';
import { ForgeonMenuItems } from '/@/settings/menuSettings';
import { FORGEON_MENU_FOLDED_KEY } from '/@/enums/cacheEnum';
import { AppDarkModeToggle, AppHelpDoc, AppProjectSelect } from '/@/components/Application';
import { useUserStore } from '/@/store/modules/user';
import { useAuthedMenu } from '../../service/permission/usePermission.ts';
import { traceRouteChange } from '/@/service/tracker/index.ts';

const props = withDefaults(defineProps<{
  withoutHeader?: boolean;
}>(), {
  withoutHeader: false,
});

const { prefixCls } = useDesign('default-layout');
const { getShowFullHeaderRef, getShowDoc, getShowProjectSelect } = useHeaderSetting();
const { getShowSidebar, getIsMixSidebar, getShowMenu } = useMenuSetting();
const { getAutoCollapse } = useMultipleTabSetting();
const userStore = useUserStore();
const { authedModules } = useAuthedMenu();
const route = useRoute();
const { getDarkMode, setDarkMode } = useRootSetting();
const { collapsed, activeMenuItem, showSubSider } = useForgeOnSider();
const hasToken = userStore.getToken;

const router = useRouter();
const { setTrack } = useTrack();
const currentPath = ref<string>(route.path);

const removeListenerFnList = ref<RemoveEventFn[]>([]);

const isDark = computed(() => getDarkMode.value === ForgeonTheme.Dark);
const isMenuCollapsed = computed(() => {
  return Boolean(Number(Persistent.getLocal(FORGEON_MENU_FOLDED_KEY) ?? '0')) || document.body.scrollWidth < 1248;
});
const currentTitle = computed(() => {
  return activeMenuItem.value?.title as string;
});
const withoutHeader = computed(() => {
  return props.withoutHeader || !getShowFullHeaderRef.value;
});

const getShowMenuUnfoldBtn = computed(() => {
  return unref(collapsed) && unref(showSubSider);
});

const projectShowAllOption = computed(() => {
  return router.currentRoute.value.name === PlatformEnterPoint.TrackingAnalysis;
});

watch(() => route.path, () => {
  currentPath.value = route.path;
});

const layoutClass = computed(() => {
  const cls: string[] = [''];
  if (unref(getIsMixSidebar) || unref(getShowMenu)) {
    cls.push('ant-layout-has-sider');
  }

  if (!unref(getShowMenu) && unref(getAutoCollapse)) {
    cls.push('ant-layout-auto-collapse-tabs');
  }

  return cls;
});

function onHandleExpand() {
  collapsed.value = false;
  Persistent.setLocal(FORGEON_MENU_FOLDED_KEY, '0');
}

async function onPathChange(options: RouteLocationRaw & { name: string }, openInNewTab?: boolean) {
  const isMainRouterItem = router.getRoutes().some((item) => item.name === options.name);
  const { target: sysRoute } = findInTrees(authedModules.value, (item) => item.name === options.name, {
    order: 'post',
  });
  if (isMainRouterItem) {
    // 主应用场景
    const targetRoute = router.resolve(options);
    if (openInNewTab) {
      window.open(targetRoute.href, '_blank');
    } else {
      router.push(targetRoute);
    }
  } else {
    router.push({ path: sysRoute?.path });
  }
}

// 外部链接跳转
async function onOutPathChange(options: { name: string; path: string }) {
  const { path: curPath } = findItemInMenuByPath(ModulesMenuConfig, options.path);
  const { path: prvPath } = findItemInMenuByPath(ModulesMenuConfig, currentPath.value);
  traceRouteChange({
    data: {
      route_name: options.name,
      route_path: options.path,
      prv_route_name: activeMenuItem.value?.key,
      prv_route_path: currentPath.value,
      business_level_cur: curPath.join('/') || '',
      business_level_prv: prvPath.join('/') || '',
    },
  });
  window.open(options.path, '_blank', 'noopener,noreferrer');
}

async function switchRole(auth?: RoleListItem) {
  if (auth?.authorityId) {
    await setUserAuthority({ authorityId: auth.authorityId });
    await setTrack('t7uzowvzhn');
    const role = await userStore.getUserInfoAction();
    if (role?.authorityId === auth.authorityId) {
      message.success('切换成功');
      document.body.style.pointerEvents = 'none';
      clearAllFetchPoolLRU();
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }
  }
}

function onLogin() {
  router.push({
    name: PlatformEnterPoint.Login,
    query: {
      redirect: router.currentRoute.value.fullPath,
    },
  });
}

function toggleDarkMode(mode?: ForgeonTheme) {
  const darkMode
      = mode || (getDarkMode.value === ForgeonTheme.Dark ? ForgeonTheme.Light : ForgeonTheme.Dark);
  setDarkMode(darkMode);
}

function handleCollapseClick() {
  collapsed.value = true;
  Persistent.setLocal(FORGEON_MENU_FOLDED_KEY, '1');
}

function handleToggleClick(event: MouseEvent, mode?: ForgeonTheme) {
  const x = event.clientX;
  const y = event.clientY;
  const endRadius = Math.hypot(Math.max(x, innerWidth - x), Math.max(y, innerHeight - y));

  // 兼容性处理
  if (!document.startViewTransition) {
    toggleDarkMode(mode as ForgeonTheme);
    return;
  }
  const transition = document.startViewTransition(() => {
    toggleDarkMode(mode);
  });

  transition.ready.then(() => {
    const clipPath = [`circle(0px at ${x}px ${y}px)`, `circle(${endRadius}px at ${x}px ${y}px)`];
    document.documentElement.animate(
      {
        clipPath: !isDark.value ? clipPath.reverse() : clipPath,
      },
      {
        duration: 600,
        easing: 'ease-in',
        pseudoElement: !isDark.value
          ? '::view-transition-old(root)'
          : '::view-transition-new(root)',
      },
    );
  });
}

function onResizeCollapseListener() {
  if (Number(Persistent.getLocal(FORGEON_MENU_FOLDED_KEY) ?? '0') !== 1) {
    collapsed.value = document.body.scrollWidth < 1248;
  }
}

onMounted(() => {
  // 监听系统主题切换 同步切换主题(需浏览器支持)
  if (window.matchMedia('(prefers-color-scheme)').media !== 'not all') {
    const curMouseEvent = {
      clientX: 0,
      clientY: window.screen.height,
    } as MouseEvent;
    const listeners = {
      dark: (mediaQueryList) => {
        if (mediaQueryList.matches) {
          if (getDarkMode.value !== ForgeonTheme.Dark) {
            handleToggleClick(curMouseEvent, ForgeonTheme.Dark);
          }
        }
      },
      light: (mediaQueryList) => {
        if (mediaQueryList.matches) {
          if (getDarkMode.value !== ForgeonTheme.Light) {
            handleToggleClick(curMouseEvent, ForgeonTheme.Light);
          }
        }
      },
    };
    const { removeEvent: darkRemoveEvent } = useEventListener({
      el: window.matchMedia('(prefers-color-scheme: dark)'),
      name: 'change',
      listener: listeners.dark,
      wait: 0,
    });
    const { removeEvent: lightRemoveEvent } = useEventListener({
      el: window.matchMedia('(prefers-color-scheme: light)'),
      name: 'change',
      listener: listeners.light,
      wait: 0,
    });

    const { removeEvent: resizeRemoveEvent } = useEventListener({
      el: window,
      name: 'resize',
      listener: onResizeCollapseListener,
    });
    removeListenerFnList.value.push(darkRemoveEvent, lightRemoveEvent, resizeRemoveEvent);
  }
});

onUnmounted(() => {
  removeListenerFnList.value.forEach((e) => e);
  removeListenerFnList.value = [];
});
onBeforeMount(() => {
  userStore.getUserInfoAction();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-default-layout';

.@{prefix-cls} {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100%;

  > .ant-layout {
    min-height: 100%;
  }

  &-main {
    // position: relative;
    width: 100%;
  }

  &-header {
    width: 100%;
    z-index: 10;
  }
}
</style>
