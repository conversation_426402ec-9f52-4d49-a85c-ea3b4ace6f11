export enum instanceType {
// 1 - 通用；2 - 编译；3 - 非编译
  Common = 1,
  Compile = 2,
  NonCompile = 3,
}
export enum workStateType {
// 1 - 空闲；2 - 检查中；3 - 更新中；4 - 重启中；5 - 离线
  Idle = 1,
  Checking = 2,
  Updating = 3,
  Restarting = 4,
  Offline = 5,
}
export const workStateOptions = [
  {
    label: '空闲',
    value: workStateType.Idle,
  },
  {
    label: '检查中',
    value: workStateType.Checking,
  },
  {
    label: '更新中',
    value: workStateType.Updating,
  },
  {
    label: '重启中',
    value: workStateType.Restarting,
  },
  {
    label: '离线',
    value: workStateType.Offline,
  },
];
export const instanceOptions = [
  {
    label: '通用',
    value: instanceType.Common,
  },
  {
    label: '编译',
    value: instanceType.Compile,
  },
  {
    label: '非编译',
    value: instanceType.NonCompile,
  },
];
export enum operationTypes {
  // 【 1, 2, 3】 - 预约 【更新、重启、禁用 】；【4，5，6】- 取消预约【更新、重启、禁用】; 【7 8 9】 - 执行【更新、重启、禁用】；10 - 修改；11 - 注册
  Update = 1,
  Restart = 2,
  Disable = 3,
  CancelUpdate = 4,
  CancelRestart = 5,
  CancelDisable = 6,
  ExecuteUpdate = 7,
  ExecuteRestart = 8,
  ExecuteDisable = 9,
  Modify = 10,
  Register = 11,
}
export const operationTypeOptions = [
  {
    label: '预约更新',
    value: operationTypes.Update,
  },
  {
    label: '预约重启',
    value: operationTypes.Restart,
  },
  {
    label: '预约禁用',
    value: operationTypes.Disable,
  },
  {
    label: '取消更新',
    value: operationTypes.CancelUpdate,
  },
  {
    label: '取消重启',
    value: operationTypes.CancelRestart,
  },
  {
    label: '取消禁用',
    value: operationTypes.CancelDisable,
  },
  {
    label: '执行更新',
    value: operationTypes.ExecuteUpdate,
  },
  {
    label: '执行重启',
    value: operationTypes.ExecuteRestart,
  },
  {
    label: '执行禁用',
    value: operationTypes.ExecuteDisable,
  },
  {
    label: '修改实例',
    value: operationTypes.Modify,
  },
  {
    label: '注册实例',
    value: operationTypes.Register,
  },
];
