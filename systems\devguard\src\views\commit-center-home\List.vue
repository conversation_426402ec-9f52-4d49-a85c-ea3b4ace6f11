<template>
  <div class="commit-center">
    <div v-for="depot in depotsList" :key="depot.prefix" class="commit-center-card">
      <div class="flex items-center">
        <TypographyText :ellipsis="{ tooltip: true }" :content="depot.name || depot.prefix" class="FO-Font-B16 mb-4 max-w-300px!" />
      </div>
      <StreamsList :streamsList="depot.streams" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { TypographyText } from 'ant-design-vue';
import { computed, ref, watch } from 'vue';
import { type ListSubmitItem, getListSubmit } from '../../api';
import StreamsList from './StreamsList.vue';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';

const forgeonConfig = useForgeonConfigStore(store);
const currentProjectId = computed(() => forgeonConfig.currentProjectId);
const projectCode = computed(() => forgeonConfig.currentProjectAlias);
const depotsList = ref<ListSubmitItem[]>();

watch(() => [currentProjectId.value, projectCode.value], async () => {
  if (!currentProjectId.value || !projectCode.value) {
    return;
  }
  const res = await getListSubmit({ projectCode: projectCode.value, id: currentProjectId.value }, { });
  depotsList.value = res.data?.data?.list;
}, { immediate: true });
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.commit-center {
  &-card {
    margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
    margin: 20px;

    &-list {
      &-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 16px;
        border: 1px solid @FO-Container-Stroke1;
        border-radius: 8px;
        background-color: @FO-Container-Fill1;

        &:hover {
          border-color: @FO-Brand-Primary-Default;
        }

        &:not(:last-child) {
          margin-bottom: 8px;
        }
      }
    }
  }
}
</style>
