<template>
  <Modal
    :width="600" :open="show" :maskClosable="false" destroyOnClose centered :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>检查实例分配规则</span>
        </span>
      </div>
    </template>
    <div class="m-[20px]">
      <RadioGroup v-model:value="assignType">
        <Radio class="h-30px flex line-height-30px" :value="1">
          <span>平均分配</span>
        </Radio>
        <Radio class="h-30px flex line-height-30px" :value="2">
          <div class="flex items-center gap-10px">
            <span>按条件分配</span>
            <Button class="flex items-center" size="small" type="primary">
              <Icon :icon="addIcon" /><span>添加条件</span>
            </Button>
          </div>
        </Radio>
      </RadioGroup>
      <div class="assign-rule-container">
        <div v-for="item in assignRule" :key="item" class="mt-10px b-rd-8px bg-FO-Container-Fill3 p-20px">
          <div class="flex items-center gap-20px">
            <DragOutlined class="drag-btn h-10px w-10px cursor-grab" />
            <div class="flex flex-1 flex-col gap-5px">
              <div class="flex">
                <span class="w-120px">若文件后缀包含：</span> <Select
                  v-model:value="fileListRef" class="file-select min-h-60px flex-1"
                  :open="false" mode="tags" style="width: 100%" :tokenSeparators="[',']" @change="fileListChange"
                />
              </div>
              <div class="flex">
                <span class="w-120px">则触发实例类型：</span><Select v-model:value="fileListRef" class="w-200px" />
              </div>
            </div>
          </div>
        </div>
        <div class="mt-10px bg-FO-Container-Fill3 px-20px py-10px">
          若不满足上述条件，则触发其他实例
        </div>
      </div>
    </div>
    <div v-if="errorText">
      {{ errorText }}
    </div>
    <template #footer>
      <div class="mt flex justify-end">
        <Button type="primary" @click="handleConfirm">
          保存
        </Button>
        <Button class="ml-2" @click="modalDestroy()">
          取消
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Modal, Radio, RadioGroup, Select } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { nextTick, onMounted, ref } from 'vue';
import addIcon from '@iconify-icons/icon-park-outline/plus';
import { Icon } from '@iconify/vue';
import { DragOutlined } from '@ant-design/icons-vue';
import { useSortable } from '../../../hooks/useSortable';

const props = defineProps<ModalBaseProps<{ updatedItem?: any }> & {
  assignType?: number;
  assignRule?: any[];
}>();
const errorText = ref('');
const fileListRef = ref<string[]>([]);
const assignType = ref(props.assignType ?? 1);
const assignRule = ref(props.assignRule ?? [{}, {}]);
function fileListChange() {
  fileListRef.value = fileListRef.value.map((item) => {
    if (!item || item.trim() === '') {
      return '';
    }
    if (item[0] !== '.') {
      return `.${item}`;
    } else {
      return item;
    }
  });
}

async function handleConfirm() {
  errorText.value = '';
  if (assignType.value === 2) {
    if (assignRule.value.some((item) => !item.fileList?.length)) {
      errorText.value = '请填写文件后缀';
      return;
    }
  }

  return props.modalConfirm({});
}

function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.assign-rule-container`) as HTMLElement;
    useSortable(el, {
      handle: `.drag-btn`,
      onEnd: async ({ oldIndex, newIndex }: { oldIndex?: number; newIndex?: number }) => {
        console.log('🚀 ~ initDrag ~ oldIndex, newIndex:', oldIndex, newIndex);
      },
    });
  });
}
onMounted(() => {
  initDrag();
});
</script>

<style lang="less" scoped>
.file-select :deep(.ant-select-selector) {
  min-height: 60px;
  align-items: flex-start;
}
</style>
