{"name": "@hg-tech/devguard", "type": "module", "version": "1.0.0", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "build:rnd": "cross-env NODE_ENV=production vite build --mode rnd", "build:pre": "cross-env NODE_ENV=production vite build --mode pre", "build:analyze": "vite build -- --analyze", "test": "run-p test:*", "test:type": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@ant-design/icons-vue": "catalog:", "@hg-tech/forgeon-style": "workspace:*", "@hg-tech/forgeon-uno-config": "workspace:^", "@hg-tech/oasis-common": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils-vue": "workspace:*", "@iconify-icons/ant-design": "^1.2.7", "@iconify-icons/icon-park-outline": "catalog:", "@iconify-icons/line-md": "^1.2.30", "@iconify/vue": "catalog:", "@micro-zoe/micro-app": "catalog:", "@types/sortablejs": "^1.15.8", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash": "catalog:", "pinia": "catalog:", "sortablejs": "^1.15.6", "unocss": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@hg-tech/configs": "workspace:^", "@types/lodash": "catalog:", "cross-env": "catalog:", "vite": "catalog:", "vite-svg-loader": "catalog:", "vue-tsc": "catalog:"}}