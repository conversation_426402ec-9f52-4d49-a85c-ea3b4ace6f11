import type { RouteRecordRaw } from 'vue-router';
import { authRoutes } from './auth.ts';
import { Exception as ExceptionPage } from '/@/views/sys/exception';
import { otherRoutes } from './others.ts';
import { PermissionPoint, PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';
import { ExceptionEnum } from '/@/enums/exceptionEnum.ts';
import { toSubSysRoute } from '../helper.tsx';

import RouterLayout from '../../layouts/RouterLayout.vue';
import PureLayout from '../../layouts/PureLayout.vue';
import MicroLayout from '/@/layouts/MicroLayout.vue';

export const routes: Readonly<RouteRecordRaw[]> = [

  toSubSysRoute({
    name: PlatformEnterPoint.SysAdmin,
    basePath: PlatformRoutePath.SysAdmin,
    url: import.meta.env.VITE_SUB_SYS_URL_ADMIN,
    title: '管理后台',
    configs: {
      'disable-patch-request': true,
    },
    routeMeta: {
      permissionDeclare: {
        scope: PlatformEnterPoint.SysAdmin,
        any: [],
      },
    },
  }),
  {
    path: '',
    component: RouterLayout,
    children: [
      {
        path: '',
        redirect: { name: PlatformEnterPoint.Home },
      },
      ...otherRoutes,
    ],
  },
  // 以下为子系统路由（带基础布局MicroLayout）
  {
    path: '',
    component: MicroLayout,
    children: [
      toSubSysRoute({
        name: PlatformEnterPoint.SysAigc,
        basePath: PlatformRoutePath.SysAigc,
        url: import.meta.env.VITE_SUB_SYS_URL_AIGC,
        title: `AI`,
        configs: {
          'keep-router-state': true,
          'disable-patch-request': import.meta.env.MODE !== 'development',
        },
        routeMeta: {
          disableRouteViewKeyAttr: true,
        },
      }),
      toSubSysRoute({
        name: PlatformEnterPoint.SysPermissionCenter,
        basePath: PlatformRoutePath.SysPermissionCenter,
        url: import.meta.env.VITE_SUB_SYS_URL_PERMISSION_CENTER,
        title: `权限管理中心`,
        routeMeta: {
          disableRouteViewKeyAttr: true,
        },
      }),
      toSubSysRoute({
        name: PlatformEnterPoint.SysDevGuard,
        basePath: PlatformRoutePath.SysDevGuard,
        url: import.meta.env.VITE_SUB_SYS_URL_DEVGUARD,
        title: `DevGuard`,
        routeMeta: {
          disableRouteViewKeyAttr: true,
        },
      }),
      toSubSysRoute({
        name: PlatformEnterPoint.Conflux,
        basePath: PlatformRoutePath.Conflux,
        url: import.meta.env.VITE_BASE_API_ORIGIN_CONFLUX,
        title: `Conflux`,
        configs: {
          'keep-router-state': true,
        },
        routeMeta: {
          disableRouteViewKeyAttr: true,
          permissionDeclare: {
            scope: PlatformEnterPoint.Conflux,
            all: ['ReadMerge', 'ViewRule'],
          },
        },
      }),
    ],
  },
  // 以下为无header路由
  {
    path: '',
    component: PureLayout,
    children: [
      {
        name: PlatformEnterPoint.P4Depots,
        path: PlatformRoutePath.P4Depots,
        component: () => import('../../views/versionControl/p4PermissionManage/index.vue'),
        meta: {
          permissionDeclare: {
            all: [PermissionPoint.P4Depots],
          },
          title: 'P4分支管理平台',
        },
      },
      {
        name: PlatformEnterPoint.Home,
        path: PlatformRoutePath.Home,
        alias: '',
        component: () => import('../../views/home/<USER>'),
      },
      {
        name: PlatformEnterPoint.EfficacyPreview,
        path: PlatformRoutePath.EfficacyPreview,
        component: () => import('../../views/temp-preview/efficacy.vue'),
        meta: {
          title: '统计',
        },
      },
      {
        name: PlatformEnterPoint.TrackingAnalysis,
        path: PlatformRoutePath.TrackingAnalysis,
        component: () => import('../../views/toolkit/tracking/analysis/index.vue'),
        meta: {
          permissionDeclare: {
            any: [PermissionPoint.TrackingAnalysis],
          },
          title: '分析平台',
        },
      },
      {
        name: PlatformEnterPoint.CloudDevice,
        path: PlatformRoutePath.CloudDevice,
        component: () => import('../../views/deptAsset/cloud/index.vue'),
        meta: {
          permissionDeclare: {
            scope: PlatformEnterPoint.DeptAsset,
            all: ['useCloudPhone'],
          },
          title: '云真机',
        },
      },
      {
        name: PlatformEnterPoint.CloudDeviceDetail,
        path: PlatformRoutePath.CloudDeviceDetail,
        component: () => import('../../views/deptAsset/cloud/detail/index.vue'),
        meta: {
          permissionDeclare: {
            scope: PlatformEnterPoint.DeptAsset,
            all: ['useCloudPhone'],
          },
          title: '云真机详情',
        },
      },
      ...authRoutes,
    ],
  },
  {
    // FIXME 用于 refreshPage 模拟刷新页面，需要不应该使用
    path: PlatformRoutePath.Redirect,
    component: RouterLayout,
    children: [
      {
        path: ':path(.*)/:_redirect_type(.*)/',
        name: PlatformEnterPoint.Redirect,
        component: () => import('../../views/sys/redirect/index.vue'),
        meta: {
          title: '重定向...',
          hideBreadcrumb: true,
        },
      },
    ],
  },
  {
    path: '/:path(.*)*',
    component: RouterLayout,
    redirect(r) {
      return {
        name: PlatformEnterPoint.NotFound,
        query: {
          status: ExceptionEnum.PAGE_NOT_FOUND,
          redirect: r.fullPath,
        },
      };
    },
    children: [
      {
        path: '/not-found',
        name: PlatformEnterPoint.NotFound,
        component: ExceptionPage,
        meta: {
          title: 'Not Found',
          hideBreadcrumb: true,
          hideMenu: true,
        },
      },
      {
        path: '/forbidden',
        name: PlatformEnterPoint.Forbidden,
        props: (r) => ({ status: Number(r.query.status) || 403 }),
        component: ExceptionPage,
        meta: {
          title: 'Forbidden',
          hideBreadcrumb: true,
          hideMenu: true,
        },
      },
    ],
  },
];
