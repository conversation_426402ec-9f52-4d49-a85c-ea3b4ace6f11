<template>
  <div class="devguard-layout">
    <ForgeonHeader
      :onHandleMenuExpand="onHandleMenuExpand"
      :showUnfoldIcon="showUnfoldIcon"
      title="提交中心"
    >
      <template #actions>
        <ForgeonProjectSelector
          class="w-160px"
          containerClass="bg-FO-Container-Fill2"
          :options="forgeonConfig.projectList"
          :value="forgeonConfig.currentProjectId"
          @select="handleProjectSelect"
        />
      </template>
    </ForgeonHeader>
    <router-view />
  </div>
</template>

<script lang="ts" setup>
import { ForgeonHeader, ForgeonProjectSelector, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { useForgeonConfigStore } from '../store/modules/forgeonConfig';
import { store } from '../store/pinia';
import { computed } from 'vue';

const forgeonConfig = useForgeonConfigStore(store);
const platformConfig = computed(() => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    return useMicroAppInject(usePlatformConfigCtx);
  } else {
    return null;
  }
});

const showUnfoldIcon = computed(() => {
  if (platformConfig.value?.data.value) {
    return !platformConfig.value.data.value.isMenuExpanded;
  } else {
    return false;
  }
});

function onHandleMenuExpand() {
  platformConfig.value?.data.value?.changeMenuExpendStatus(true);
}
function handleProjectSelect(id: number | undefined) {
  if (id !== undefined) {
    forgeonConfig.setCurrentProjectId(id);
  }
}
</script>
