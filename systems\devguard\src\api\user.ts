import { requestService } from '../../src/services/req';
import type { PermissionBaseRes } from './_common';

export interface SysUserInfo {
  /**
   * 鹰角id
   */
  hgId?: string;
  /** 鹰角账号 */
  hgAccount?: string;
  name?: string;
  nickname?: string;
  /**
   * 组织id
   */
  organization?: number;
  /**
   * 组织编码
   */
  organizationNo?: string;
  /**
   * 组织路径
   */
  orgPath?: string;
  /**
   * 企业邮箱
   */
  enterpriseEmail?: string;
  avatar?: string;
  feishuUnionId?: string;
  feishuOpenId?: string;
  /**
   * 是否外包
   */
  outsourceFlag?: boolean;
}

export const queryUserList = requestService.GET<
  {
    /**
     * 必填，姓名/昵称/邮箱/拼音复合搜索
     */
    query: string;
  },
  Record<string, never>,
  PermissionBaseRes<SysUserInfo[]>
>(`/api/auth/v1/user/search`);
