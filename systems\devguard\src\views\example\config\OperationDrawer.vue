<template>
  <Drawer
    :open="show"
    width="900px"
    :closable="false"
    placement="right"
    @afterOpenChange="v => !v && modalDestroy()"
    @close="() => modalConfirm()"
  >
    <div v-if="gridOptions.data?.length">
      <FilterWrapper
        v-model:form="searchForm"
        class="mb-12px"
      />
      <BasicVxeTable :options="gridOptions" />
      <div class="flex justify-end p-4">
        <div class="w-100% flex items-center justify-between">
          <div>
            共计
            {{ pagerConfig.total }}
            条数据
          </div>
          <Pagination
            v-model:current="pagerConfig.currentPage"
            v-model:pageSize="pagerConfig.pageSize"
            :total="pagerConfig.total"
            showSizeChanger
            :pageSizeOptions="['10', '20', '50', '100']"
            @change="handlePageData"
          />
        </div>
      </div>
    </div>

    <div v-else class="h-full flex items-center justify-center">
      <Empty :image="emptyImg" description="暂无数据" />
    </div>

    <template #title>
      <div class="flex items-center justify-between">
        操作历史
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end">
        <Button @click="close">
          关闭
        </Button>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { Button, Drawer, Empty, Pagination } from 'ant-design-vue';
import { FilterWrapper } from './filter-wrapper.tsx';
import { useForgeonConfigStore } from '../../../store/modules/forgeonConfig';
import { store } from '../../../store/pinia';
import { useRouter } from 'vue-router';
import { onMounted, reactive, ref } from 'vue';
import FillAdminIcon from '../../../assets/icons/fill-admin.svg?component';
import dayjs from 'dayjs';
import { type VxeGridProps, BasicVxeTable, EllipsisText } from '@hg-tech/oasis-common';
import { type OperationsListItem, getOperationsListApi } from '../../../api/index.ts';
import { operationTypeOptions } from './type.data.ts';

const props = defineProps<ModalBaseProps & {

}>();

const emptyImg = Empty.PRESENTED_IMAGE_SIMPLE;
const router = useRouter();
const routeParams = router.currentRoute.value.params;
const forgeonConfig = useForgeonConfigStore(store);
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const pagerConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 20,
});
const searchForm = ref();
const gridOptions = reactive<VxeGridProps<OperationsListItem>>({
  showOverflow: true,
  maxHeight: '100%',
  border: 'none',
  columns: [
    { field: 'point', title: '', width: '30px', align: 'center', slots: { default() {
      return <div class="h-6px w-6px rd-half bg-FO-Container-Fill5" />;
    } } },
    { field: 'execTime', title: '操作时间', width: '170px', align: 'left', slots: { default({ row }) {
      return dayjs(row.execTime).format('YYYY-MM-DD HH:mm:ss');
    } } },
    { field: 'finishTime', title: '完成时间', width: '170px', align: 'left', slots: { default({ row }) {
      return dayjs(row.finishTime).format('YYYY-MM-DD HH:mm:ss');
    } } },
    { field: 'operator', title: '操作人', slots: { default({ row }) {
      return (
        <div class="w-full flex items-center gap-8px">
          {row.operator.headerImg
            ? (
              <img class="size-24px flex-shrink-0 b-1 b-FO-Container-Stroke0 rounded-full b-solid" src={row.operator.headerImg} />
            )
            : (
              <div class="size-24px flex items-center justify-center rounded-full bg-FO-Datavis-Purple3 c-FO-Datavis-Purple1">
                <FillAdminIcon />
              </div>
            )}
          <EllipsisText class="FO-Font-R14 min-w-0 flex-1 text-FO-Content-Text1">
            {row.operator.nickName}
          </EllipsisText>
        </div>
      );
    } } },
    { field: 'operationType', title: '操作类型', slots: { default({ row }) {
      return operationTypeOptions.find((e) => e.value === row.operationType)?.label;
    } } },
    { field: 'assetN', title: '操作详情', slots: { default({ row }) {
      return row;
    } } },
  ],
  data: [],
  columnConfig: {
    resizable: false,
  },
});
async function handlePageData() {
  try {
    gridOptions.loading = true;
    const res = await getOperationsListApi({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, page: pagerConfig.currentPage, pageSize: pagerConfig.pageSize });
    pagerConfig.total = res?.data?.data?.total || 0;
    gridOptions.data = res?.data?.data?.list || [
      {
        checkInstance: {
          id: 3,
          ipAddress: 'http://*************:65348',
          workspace: 'arl_main_project_1',
          instanceType: 1,
          workState: 5,
          pendingState: 0,
          disabled: false,
          name: '海祺分支1号实例++',
          bootTimestamp: 1755522202,
        },
        operator: {
          ID: 773,
          CreatedAt: '2025-04-27T11:20:05.359+08:00',
          UpdatedAt: '2025-04-27T11:26:24.565+08:00',
          uuid: 'cd7aa65c-ca3c-4a63-b17c-12dd584372ee',
          userName: 'wanghongyi',
          nickName: '王弘毅(mikyi)',
          sideMode: 'dark',
          headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: '0001-01-01T00:00:00Z',
            UpdatedAt: '0001-01-01T00:00:00Z',
            DeletedAt: null,
            authorityId: 0,
            authorityName: '',
            parentId: 0,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '',
          email: '<EMAIL>',
          enable: 1,
          authType: 1,
          openID: '',
        },
        execTime: '2025-08-19T15:56:16.171+08:00',
        finishTime: '2025-08-19T15:56:16.171+08:00',
        operationType: 10,
        modifications: [
          {
            modifyKey: 'name',
            oldValue: '海祺分支1号实例',
            newValue: '海祺分支1号实例++',
          },
        ],
      },
    ];
  } finally {
    gridOptions.loading = false;
    gridOptions.data = [
      {
        checkInstance: {
          id: 3,
          ipAddress: 'http://*************:65348',
          workspace: 'arl_main_project_1',
          instanceType: 1,
          workState: 5,
          pendingState: 0,
          disabled: false,
          name: '海祺分支1号实例++',
          bootTimestamp: 1755522202,
        },
        operator: {
          ID: 773,
          CreatedAt: '2025-04-27T11:20:05.359+08:00',
          UpdatedAt: '2025-04-27T11:26:24.565+08:00',
          uuid: 'cd7aa65c-ca3c-4a63-b17c-12dd584372ee',
          userName: 'wanghongyi',
          nickName: '王弘毅(mikyi)',
          sideMode: 'dark',
          headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
          baseColor: '#fff',
          activeColor: '#1890ff',
          authorityId: 888,
          authority: {
            CreatedAt: '0001-01-01T00:00:00Z',
            UpdatedAt: '0001-01-01T00:00:00Z',
            DeletedAt: null,
            authorityId: 0,
            authorityName: '',
            parentId: 0,
            dataAuthorityId: null,
            children: null,
            menus: null,
            defaultRouter: '',
          },
          authorities: null,
          phone: '',
          email: '<EMAIL>',
          enable: 1,
          authType: 1,
          openID: '',
        },
        execTime: '2025-08-19T15:56:16.171+08:00',
        finishTime: '2025-08-19T15:56:16.171+08:00',
        operationType: 10,
        modifications: [
          {
            modifyKey: 'name',
            oldValue: '海祺分支1号实例',
            newValue: '海祺分支1号实例++',
          },
        ],
      },
    ];
  }
}

function close() {
  return props.modalConfirm();
}

onMounted(() => {
  handlePageData();
});
</script>
